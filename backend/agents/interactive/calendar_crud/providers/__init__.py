"""
Calendar providers package.

This package provides implementations of different calendar providers,
including Google Calendar and Calendly.
"""

from .base import CalendarProvider, ProviderCapability
from .models import (
    CalendarEvent,
    CalendarEventCreate,
    CalendarEventUpdate,
    EventStatus,
    Attendee,
    TimeSlot,
    FreeBusyRequest,
    FreeBusyResponse
)
from .exceptions import (
    CalendarProviderError,
    AuthenticationError,
    ProviderAuthError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    ValidationError,
    WebhookError,
    ConfigurationError,
    NetworkError,
    ServiceUnavailableError,
    UnsupportedOperationError
)
from .google import GoogleCalendarProvider
from .calendly import CalendlyProvider
from .factory import (
    get_provider,
    get_available_providers,
    register_provider,
    get_provider_capabilities,
    load_capability_matrix,
    CalendarProviderFactory
)

__all__ = [
    # Base classes
    "CalendarProvider",
    "ProviderCapability",

    # Models
    "CalendarEvent",
    "CalendarEventCreate",
    "CalendarEventUpdate",
    "EventStatus",
    "Attendee",
    "TimeSlot",
    "FreeBusyRequest",
    "FreeBusyResponse",

    # Exceptions
    "CalendarProviderError",
    "AuthenticationError",
    "ProviderAuthError",
    "ResourceNotFoundError",
    "PermissionError",
    "RateLimitError",
    "ValidationError",
    "WebhookError",
    "ConfigurationError",
    "NetworkError",
    "ServiceUnavailableError",
    "UnsupportedOperationError",

    # Providers
    "GoogleCalendarProvider",
    "CalendlyProvider",

    # Factory
    "get_provider",
    "get_available_providers",
    "register_provider",
    "get_provider_capabilities",
    "load_capability_matrix",
    "CalendarProviderFactory"
]
