"""
Tests for Google Calendar provider token fetching from auth-service.

This module tests the token fetching functionality, including caching behavior
and auth-service integration.
"""

import pytest
import httpx
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, MagicMock

from backend.agents.interactive.calendar_crud.providers.google.client import GoogleCalendarClient, _token_cache
from backend.agents.interactive.calendar_crud.providers.exceptions import ProviderAuthError, AuthenticationError


@pytest.fixture
def mock_auth_service_base():
    """Mock AUTH_SERVICE_BASE environment variable."""
    with patch('backend.config.settings.auth_service_base', 'http://auth.local'):
        yield


@pytest.fixture
def mock_firm_id():
    """Mock firm ID."""
    return "test-firm-123"


@pytest.fixture
def clear_token_cache():
    """Clear the token cache before each test."""
    _token_cache.clear()
    yield
    _token_cache.clear()


@pytest.fixture
def google_client():
    """Create a Google Calendar client instance."""
    return GoogleCalendarClient()


@pytest.mark.asyncio
class TestGoogleTokenFetch:
    """Test Google Calendar token fetching functionality."""

    async def test_token_fetch_from_auth_service(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test fetching token from auth-service."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "test-access-token-123",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }

            # Fetch token
            token = await google_client._get_token(mock_firm_id)

            # Verify token
            assert token == "test-access-token-123"

            # Verify token is cached
            cache_key = f"{mock_firm_id}:google"
            assert cache_key in _token_cache
            assert _token_cache[cache_key]["access_token"] == "test-access-token-123"

            # Verify the mock was called with correct URL
            mock_get.assert_called_once_with("http://auth.local/tokens/test-firm-123/google")

    async def test_token_cache_hit(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that second call uses cached token and doesn't hit auth-service."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "test-access-token-123",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }

            # First call - should hit auth-service
            token1 = await google_client._get_token(mock_firm_id)
            assert token1 == "test-access-token-123"
            assert mock_get.call_count == 1

            # Second call - should use cache, not hit auth-service
            token2 = await google_client._get_token(mock_firm_id)
            assert token2 == "test-access-token-123"
            assert mock_get.call_count == 1  # Still 1, not 2


    async def test_token_cache_expiry(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that expired tokens are refetched from auth-service."""
        # Pre-populate cache with expired token
        cache_key = f"{mock_firm_id}:google"
        _token_cache[cache_key] = {
            "access_token": "expired-token",
            "expires_at": datetime.now() - timedelta(seconds=10),  # Expired
            "cached_at": datetime.now() - timedelta(seconds=300)
        }

        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "new-access-token-456",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }

            # Fetch token - should refresh from auth-service
            token = await google_client._get_token(mock_firm_id)
            assert token == "new-access-token-456"
            assert mock_get.call_count == 1

        # Verify cache is updated
        assert _token_cache[cache_key]["access_token"] == "new-access-token-456"


    async def test_auth_service_404_error(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that 404 from auth-service raises ProviderAuthError."""
        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPNonRetryableError
            mock_get.side_effect = HTTPNonRetryableError("HTTP 404: Firm not found")

            # Should raise ProviderAuthError
            with pytest.raises(ProviderAuthError) as exc_info:
                await google_client._get_token(mock_firm_id)

            assert "Auth-service error" in str(exc_info.value)


    async def test_auth_service_401_error(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that 401 from auth-service raises ProviderAuthError."""
        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPNonRetryableError
            mock_get.side_effect = HTTPNonRetryableError("HTTP 401: Unauthorized")

            # Should raise ProviderAuthError
            with pytest.raises(ProviderAuthError) as exc_info:
                await google_client._get_token(mock_firm_id)

            assert "Auth-service error" in str(exc_info.value)


    async def test_auth_service_500_error(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that 500 from auth-service raises AuthenticationError after retries."""
        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPRetryableError
            mock_get.side_effect = HTTPRetryableError("HTTP 500: Internal server error")

            # Should raise AuthenticationError (after retries)
            with pytest.raises(AuthenticationError) as exc_info:
                await google_client._get_token(mock_firm_id)

            assert "Failed to authenticate with Google Calendar" in str(exc_info.value)


    async def test_missing_access_token_in_response(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that missing access_token in response raises AuthenticationError."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
                # Missing access_token
            }

            # Should raise AuthenticationError
            with pytest.raises(AuthenticationError) as exc_info:
                await google_client._get_token(mock_firm_id)

            assert "No access_token in auth-service response" in str(exc_info.value)


    async def test_default_ttl_when_no_expires_at(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that default 250s TTL is used when expires_at is not provided."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "test-access-token-123"
                # Missing expires_at
            }

            # Fetch token
            before_time = datetime.now()
            token = await google_client._get_token(mock_firm_id)
            after_time = datetime.now()

            # Verify token
            assert token == "test-access-token-123"

            # Verify cache has default TTL (250s)
            cache_key = f"{mock_firm_id}:google"
            cached_token = _token_cache[cache_key]
            expires_at = cached_token["expires_at"]

            # Should expire approximately 250 seconds from now
            expected_min = before_time + timedelta(seconds=249)
            expected_max = after_time + timedelta(seconds=251)
            assert expected_min <= expires_at <= expected_max


    async def test_calendar_list_request_with_auth_header(self, mock_auth_service_base, mock_firm_id, clear_token_cache, google_client):
        """Test that calendar list request includes Authorization header."""
        # Mock the SharedHTTPClient methods
        with patch('shared.http.SharedHTTPClient.get') as mock_get, \
             patch('shared.http.SharedHTTPClient.request') as mock_request:

            # Mock auth-service response
            mock_get.return_value = {
                "access_token": "test-access-token-123",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }

            # Mock Google Calendar API response
            mock_request.return_value = {
                "items": [
                    {
                        "id": "primary",
                        "summary": "Test Calendar",
                        "primary": True
                    }
                ]
            }

            # Make calendar list request
            response = await google_client.get_calendar_list(mock_firm_id)

            # Verify response
            assert "items" in response
            assert len(response["items"]) == 1
            assert response["items"][0]["summary"] == "Test Calendar"

            # Verify Authorization header was set in the request call
            mock_request.assert_called_once()
            call_args = mock_request.call_args
            headers = call_args[1]["headers"]
            assert "Authorization" in headers
            assert headers["Authorization"] == "Bearer test-access-token-123"
