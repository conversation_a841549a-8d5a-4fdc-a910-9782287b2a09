"""
Tests for Calendly provider token fetching from auth-service.

This module tests the token fetching functionality, including caching behavior
and auth-service integration.
"""

import pytest
import httpx
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, MagicMock

from backend.agents.interactive.calendar_crud.providers.calendly.client import CalendlyClient, _token_cache
from backend.agents.interactive.calendar_crud.providers.exceptions import ProviderAuthError, AuthenticationError


@pytest.fixture
def mock_auth_service_base():
    """Mock AUTH_SERVICE_BASE environment variable."""
    with patch('backend.config.settings.auth_service_base', 'http://auth.local'):
        yield


@pytest.fixture
def mock_firm_id():
    """Mock firm ID."""
    return "test-firm-123"


@pytest.fixture
def clear_token_cache():
    """Clear the token cache before each test."""
    _token_cache.clear()
    yield
    _token_cache.clear()


@pytest.fixture
def calendly_client():
    """Create a Calendly client instance."""
    return CalendlyClient()


@pytest.mark.asyncio
class TestCalendlyTokenFetch:
    """Test Calendly token fetching functionality."""

    async def test_token_fetch_from_auth_service(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test fetching token from auth-service."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "test-calendly-token-123",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }
            
            # Fetch token
            token = await calendly_client._get_token(mock_firm_id)
            
            # Verify token
            assert token == "test-calendly-token-123"
            
            # Verify token is cached
            cache_key = f"{mock_firm_id}:calendly"
            assert cache_key in _token_cache
            assert _token_cache[cache_key]["access_token"] == "test-calendly-token-123"
            
            # Verify the mock was called with correct URL
            mock_get.assert_called_once_with("http://auth.local/tokens/test-firm-123/calendly")

    async def test_token_cache_hit(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that second call uses cached token and doesn't hit auth-service."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "test-calendly-token-123",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }
            
            # First call - should hit auth-service
            token1 = await calendly_client._get_token(mock_firm_id)
            assert token1 == "test-calendly-token-123"
            assert mock_get.call_count == 1
            
            # Second call - should use cache, not hit auth-service
            token2 = await calendly_client._get_token(mock_firm_id)
            assert token2 == "test-calendly-token-123"
            assert mock_get.call_count == 1  # Still 1, not 2

    async def test_token_cache_expiry(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that expired tokens are refetched from auth-service."""
        # Pre-populate cache with expired token
        cache_key = f"{mock_firm_id}:calendly"
        _token_cache[cache_key] = {
            "access_token": "expired-calendly-token",
            "expires_at": datetime.now() - timedelta(seconds=10),  # Expired
            "cached_at": datetime.now() - timedelta(seconds=300)
        }
        
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "new-calendly-token-456",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }
            
            # Fetch token - should refresh from auth-service
            token = await calendly_client._get_token(mock_firm_id)
            assert token == "new-calendly-token-456"
            assert mock_get.call_count == 1
            
            # Verify cache is updated
            assert _token_cache[cache_key]["access_token"] == "new-calendly-token-456"

    async def test_auth_service_404_error(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that 404 from auth-service raises ProviderAuthError."""
        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPNonRetryableError
            mock_get.side_effect = HTTPNonRetryableError("HTTP 404: Firm not found")
            
            # Should raise ProviderAuthError
            with pytest.raises(ProviderAuthError) as exc_info:
                await calendly_client._get_token(mock_firm_id)
            
            assert "Auth-service error" in str(exc_info.value)

    async def test_auth_service_401_error(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that 401 from auth-service raises ProviderAuthError."""
        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPNonRetryableError
            mock_get.side_effect = HTTPNonRetryableError("HTTP 401: Unauthorized")
            
            # Should raise ProviderAuthError
            with pytest.raises(ProviderAuthError) as exc_info:
                await calendly_client._get_token(mock_firm_id)
            
            assert "Auth-service error" in str(exc_info.value)

    async def test_auth_service_500_error(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that 500 from auth-service raises AuthenticationError after retries."""
        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPRetryableError
            mock_get.side_effect = HTTPRetryableError("HTTP 500: Internal server error")
            
            # Should raise AuthenticationError (after retries)
            with pytest.raises(AuthenticationError) as exc_info:
                await calendly_client._get_token(mock_firm_id)
            
            assert "Failed to authenticate with Calendly" in str(exc_info.value)

    async def test_missing_access_token_in_response(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that missing access_token in response raises AuthenticationError."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
                # Missing access_token
            }
            
            # Should raise AuthenticationError
            with pytest.raises(AuthenticationError) as exc_info:
                await calendly_client._get_token(mock_firm_id)
            
            assert "No access_token in auth-service response" in str(exc_info.value)

    async def test_default_ttl_when_no_expires_at(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that default 250s TTL is used when expires_at is not provided."""
        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "test-calendly-token-123"
                # Missing expires_at
            }
            
            # Fetch token
            before_time = datetime.now()
            token = await calendly_client._get_token(mock_firm_id)
            after_time = datetime.now()
            
            # Verify token
            assert token == "test-calendly-token-123"
            
            # Verify cache has default TTL (250s)
            cache_key = f"{mock_firm_id}:calendly"
            cached_token = _token_cache[cache_key]
            expires_at = cached_token["expires_at"]
            
            # Should expire approximately 250 seconds from now
            expected_min = before_time + timedelta(seconds=249)
            expected_max = after_time + timedelta(seconds=251)
            assert expected_min <= expires_at <= expected_max

    async def test_user_request_with_auth_header(self, mock_auth_service_base, mock_firm_id, clear_token_cache, calendly_client):
        """Test that user request includes Authorization header."""
        # Mock the SharedHTTPClient methods
        with patch('shared.http.SharedHTTPClient.get') as mock_get, \
             patch('shared.http.SharedHTTPClient.request') as mock_request:
            
            # Mock auth-service response
            mock_get.return_value = {
                "access_token": "test-calendly-token-123",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }
            
            # Mock Calendly API response
            mock_request.return_value = {
                "resource": {
                    "uri": "https://api.calendly.com/users/test-user-123",
                    "name": "Test User",
                    "email": "<EMAIL>"
                }
            }
            
            # Make user request
            response = await calendly_client.get_user(mock_firm_id)
            
            # Verify response
            assert "resource" in response
            assert response["resource"]["name"] == "Test User"
            
            # Verify Authorization header was set in the request call
            mock_request.assert_called_once()
            call_args = mock_request.call_args
            headers = call_args[1]["headers"]
            assert "Authorization" in headers
            assert headers["Authorization"] == "Bearer test-calendly-token-123"

    async def test_separate_cache_per_provider(self, mock_auth_service_base, mock_firm_id, clear_token_cache):
        """Test that Google and Calendly tokens are cached separately."""
        # Import both clients
        from backend.agents.interactive.calendar_crud.providers.google.client import GoogleCalendarClient, _token_cache as google_cache
        from backend.agents.interactive.calendar_crud.providers.calendly.client import CalendlyClient, _token_cache as calendly_cache
        
        google_client = GoogleCalendarClient()
        calendly_client = CalendlyClient()
        
        # Mock the SharedHTTPClient.get method for both providers
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            # Configure mock to return different tokens based on URL
            def mock_get_side_effect(url):
                if "google" in url:
                    return {
                        "access_token": "google-token-123",
                        "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
                    }
                elif "calendly" in url:
                    return {
                        "access_token": "calendly-token-456",
                        "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
                    }
                else:
                    raise ValueError(f"Unexpected URL: {url}")
            
            mock_get.side_effect = mock_get_side_effect
            
            # Fetch tokens from both providers
            google_token = await google_client._get_token(mock_firm_id)
            calendly_token = await calendly_client._get_token(mock_firm_id)
            
            # Verify tokens are different
            assert google_token == "google-token-123"
            assert calendly_token == "calendly-token-456"
            
            # Verify separate cache keys
            google_cache_key = f"{mock_firm_id}:google"
            calendly_cache_key = f"{mock_firm_id}:calendly"
            
            assert google_cache_key in google_cache
            assert calendly_cache_key in calendly_cache
            
            assert google_cache[google_cache_key]["access_token"] == "google-token-123"
            assert calendly_cache[calendly_cache_key]["access_token"] == "calendly-token-456"
