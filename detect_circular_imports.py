#!/usr/bin/env python3
"""
Script to detect circular imports in the providers package.
"""

import ast
import os
import sys
from collections import defaultdict, deque
from typing import Dict, List, Set, Tuple

def extract_imports(file_path: str) -> List[str]:
    """Extract import statements from a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
        
        return imports
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return []

def find_python_files(directory: str) -> List[str]:
    """Find all Python files in a directory."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def normalize_module_name(import_name: str, file_path: str, base_dir: str) -> str:
    """Normalize import names to module paths."""
    # Convert file path to module name
    rel_path = os.path.relpath(file_path, base_dir)
    if rel_path.endswith('__init__.py'):
        module_path = os.path.dirname(rel_path).replace(os.sep, '.')
    else:
        module_path = rel_path[:-3].replace(os.sep, '.')  # Remove .py
    
    # Handle relative imports
    if import_name.startswith('.'):
        # Relative import
        parts = module_path.split('.')
        level = len(import_name) - len(import_name.lstrip('.'))
        if level > len(parts):
            return import_name  # Can't resolve
        
        base_parts = parts[:-level] if level > 0 else parts
        import_parts = import_name.lstrip('.').split('.') if import_name.lstrip('.') else []
        return '.'.join(base_parts + import_parts)
    
    return import_name

def build_dependency_graph(base_dir: str) -> Dict[str, Set[str]]:
    """Build a dependency graph from Python files."""
    files = find_python_files(base_dir)
    graph = defaultdict(set)
    
    for file_path in files:
        imports = extract_imports(file_path)
        
        # Convert file path to module name
        rel_path = os.path.relpath(file_path, base_dir)
        if rel_path.endswith('__init__.py'):
            module_name = os.path.dirname(rel_path).replace(os.sep, '.')
        else:
            module_name = rel_path[:-3].replace(os.sep, '.')  # Remove .py
        
        # Only consider imports within our package
        package_prefix = 'backend.agents.interactive.calendar_crud.providers'
        
        for import_name in imports:
            normalized = normalize_module_name(import_name, file_path, base_dir)
            
            # Only track imports within the providers package
            if (normalized.startswith(package_prefix) or 
                normalized.startswith('.') or
                normalized in ['base', 'models', 'exceptions', 'factory', 'google', 'calendly']):
                
                # Convert to full module path
                if not normalized.startswith(package_prefix):
                    if normalized.startswith('.'):
                        # Handle relative imports
                        current_package = '.'.join(module_name.split('.')[:-1])
                        if normalized == '.':
                            normalized = current_package
                        else:
                            normalized = current_package + normalized
                    else:
                        # Assume it's within the providers package
                        normalized = f"{package_prefix}.{normalized}"
                
                graph[module_name].add(normalized)
    
    return graph

def find_cycles(graph: Dict[str, Set[str]]) -> List[List[str]]:
    """Find cycles in the dependency graph using DFS."""
    visited = set()
    rec_stack = set()
    cycles = []
    
    def dfs(node: str, path: List[str]) -> bool:
        if node in rec_stack:
            # Found a cycle
            cycle_start = path.index(node)
            cycle = path[cycle_start:] + [node]
            cycles.append(cycle)
            return True
        
        if node in visited:
            return False
        
        visited.add(node)
        rec_stack.add(node)
        path.append(node)
        
        for neighbor in graph.get(node, set()):
            if neighbor in graph:  # Only follow nodes we have in our graph
                dfs(neighbor, path)
        
        rec_stack.remove(node)
        path.pop()
        return False
    
    for node in graph:
        if node not in visited:
            dfs(node, [])
    
    return cycles

def main():
    """Main function to detect circular imports."""
    providers_dir = "backend/agents/interactive/calendar_crud/providers"
    
    if not os.path.exists(providers_dir):
        print(f"Directory {providers_dir} not found")
        return
    
    print("🔍 Analyzing import dependencies in providers package...\n")
    
    # Build dependency graph
    graph = build_dependency_graph(".")
    
    # Filter to only providers package
    providers_graph = {}
    package_prefix = "backend.agents.interactive.calendar_crud.providers"
    
    for module, deps in graph.items():
        if module.startswith(package_prefix):
            providers_deps = set()
            for dep in deps:
                if dep.startswith(package_prefix):
                    providers_deps.add(dep)
            if providers_deps:
                providers_graph[module] = providers_deps
    
    print("📊 Dependency Graph:")
    for module, deps in sorted(providers_graph.items()):
        short_module = module.replace(package_prefix + ".", "")
        short_deps = [dep.replace(package_prefix + ".", "") for dep in deps]
        print(f"  {short_module} -> {short_deps}")
    
    print("\n🔄 Checking for circular imports...")
    
    # Find cycles
    cycles = find_cycles(providers_graph)
    
    if cycles:
        print(f"\n❌ Found {len(cycles)} circular import(s):")
        for i, cycle in enumerate(cycles, 1):
            short_cycle = [node.replace(package_prefix + ".", "") for node in cycle]
            print(f"\n  Cycle {i}: {' -> '.join(short_cycle)}")
    else:
        print("\n✅ No circular imports detected!")
    
    # Analyze specific problematic imports
    print("\n📋 Import Analysis:")
    
    # Check __init__.py imports
    init_file = os.path.join(providers_dir, "__init__.py")
    if os.path.exists(init_file):
        imports = extract_imports(init_file)
        print(f"\n  providers/__init__.py imports:")
        for imp in imports:
            if not imp.startswith('backend.agents.interactive.calendar_crud.providers'):
                print(f"    - {imp}")
    
    # Check factory.py imports
    factory_file = os.path.join(providers_dir, "factory.py")
    if os.path.exists(factory_file):
        imports = extract_imports(factory_file)
        print(f"\n  providers/factory.py imports:")
        for imp in imports:
            if not imp.startswith('backend.agents.interactive.calendar_crud.providers'):
                print(f"    - {imp}")

if __name__ == "__main__":
    main()
