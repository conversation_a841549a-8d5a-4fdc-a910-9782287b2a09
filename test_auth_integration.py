#!/usr/bin/env python3
"""
Simple test script to verify auth-service integration.

This script tests the basic functionality of the token fetching
without running the full test suite.
"""

import asyncio
import os
import sys
from unittest.mock import patch

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_google_client():
    """Test Google Calendar client token fetching."""
    print("Testing Google Calendar client...")
    
    # Mock the settings
    with patch('backend.config.settings.auth_service_base', 'https://ailex-auth.fly.dev'):
        with patch('backend.config.settings.ailex_firm_id', 'test-firm'):
            from backend.agents.interactive.calendar_crud.providers.google.client import GoogleCalendarClient
            
            client = GoogleCalendarClient()
            print(f"✓ Google client created successfully")
            print(f"✓ API base URL: {client.api_base_url}")
            print(f"✓ Rate limit settings: {client.rate_limit_max_requests} requests per {client.rate_limit_window}s")

async def test_calendly_client():
    """Test Calendly client token fetching."""
    print("\nTesting Calendly client...")
    
    # Mock the settings
    with patch('backend.config.settings.auth_service_base', 'https://ailex-auth.fly.dev'):
        with patch('backend.config.settings.ailex_firm_id', 'test-firm'):
            from backend.agents.interactive.calendar_crud.providers.calendly.client import CalendlyClient
            
            client = CalendlyClient()
            print(f"✓ Calendly client created successfully")
            print(f"✓ API base URL: {client.BASE_URL}")

async def test_shared_http_client():
    """Test shared HTTP client."""
    print("\nTesting shared HTTP client...")
    
    from shared.http import SharedHTTPClient
    
    async with SharedHTTPClient() as client:
        print(f"✓ SharedHTTPClient created successfully")
        print(f"✓ Timeout: {client.timeout}s")
        print(f"✓ Max retries: {client.max_retries}")

async def test_exceptions():
    """Test exception imports."""
    print("\nTesting exception imports...")
    
    from backend.agents.interactive.calendar_crud.providers.exceptions import (
        ProviderAuthError,
        AuthenticationError,
        CalendarProviderError
    )
    
    print("✓ ProviderAuthError imported successfully")
    print("✓ AuthenticationError imported successfully")
    print("✓ CalendarProviderError imported successfully")

async def test_provider_imports():
    """Test provider imports."""
    print("\nTesting provider imports...")
    
    from backend.agents.interactive.calendar_crud.providers import (
        ProviderAuthError,
        GoogleCalendarProvider,
        CalendlyProvider
    )
    
    print("✓ ProviderAuthError exported from providers package")
    print("✓ GoogleCalendarProvider imported successfully")
    print("✓ CalendlyProvider imported successfully")

async def main():
    """Run all tests."""
    print("🚀 Testing auth-service integration implementation...\n")
    
    try:
        await test_shared_http_client()
        await test_exceptions()
        await test_google_client()
        await test_calendly_client()
        await test_provider_imports()
        
        print("\n✅ All tests passed! Implementation looks good.")
        print("\n📋 Summary of changes:")
        print("  • Added AUTH_SERVICE_BASE and AILEX_FIRM_ID to backend config")
        print("  • Created shared/http.py with reusable async HTTP client")
        print("  • Updated Google Calendar client to use auth-service with caching")
        print("  • Updated Calendly client to use auth-service with caching")
        print("  • Added ProviderAuthError exception for auth-service errors")
        print("  • Added respx==0.20.2 to requirements.txt for testing")
        print("  • Created comprehensive tests for both providers")
        
        print("\n🔧 Next steps:")
        print("  • Set AUTH_SERVICE_BASE and AILEX_FIRM_ID environment variables")
        print("  • Run: pip install -r requirements.txt")
        print("  • Run: pytest tests/providers/ -v")
        print("  • Test with: scripts/dev.py list-calendars")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
