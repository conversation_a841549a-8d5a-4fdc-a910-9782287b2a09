#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix all tests to use unittest.mock instead of respx.
"""

import re

def fix_google_tests():
    """Fix the Google test file."""
    with open('tests/providers/test_google_token_fetch.py', 'r') as f:
        content = f.read()
    
    # Remove respx import and add MagicMock
    content = re.sub(r'import respx\n', '', content)
    
    # Remove @respx.mock decorators
    content = re.sub(r'    @respx\.mock\n', '', content)
    
    # Replace respx.get().mock() patterns with patch
    def replace_respx_pattern(match):
        url = match.group(1)
        response_data = match.group(2)
        
        return f'''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {response_data}'''
    
    # Pattern for respx.get().mock() calls
    pattern = r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(200, json=(\{[^}]+\})\)\s*\)'
    content = re.sub(pattern, replace_respx_pattern, content, flags=re.MULTILINE | re.DOTALL)
    
    # Fix error response patterns
    def replace_error_pattern(match):
        url = match.group(1)
        status_code = match.group(2)
        error_data = match.group(3)
        
        if status_code == '404' or status_code == '401':
            exception_type = 'HTTPNonRetryableError'
        else:
            exception_type = 'HTTPRetryableError'
        
        return f'''        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import {exception_type}
            mock_get.side_effect = {exception_type}("HTTP {status_code}: {error_data}")'''
    
    # Pattern for error responses
    error_pattern = r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\((\d+), json=(\{[^}]+\})\)\s*\)'
    content = re.sub(error_pattern, replace_error_pattern, content, flags=re.MULTILINE | re.DOTALL)
    
    with open('tests/providers/test_google_token_fetch.py', 'w') as f:
        f.write(content)
    
    print("Fixed Google tests!")

def fix_calendly_tests():
    """Fix the Calendly test file."""
    with open('tests/providers/test_calendly_token_fetch.py', 'r') as f:
        content = f.read()
    
    # Remove respx import and add MagicMock
    content = re.sub(r'import respx\n', '', content)
    
    # Remove @respx.mock decorators
    content = re.sub(r'    @respx\.mock\n', '', content)
    
    # Replace respx.get().mock() patterns with patch
    def replace_respx_pattern(match):
        url = match.group(1)
        response_data = match.group(2)
        
        return f'''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {response_data}'''
    
    # Pattern for respx.get().mock() calls
    pattern = r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(200, json=(\{[^}]+\})\)\s*\)'
    content = re.sub(pattern, replace_respx_pattern, content, flags=re.MULTILINE | re.DOTALL)
    
    with open('tests/providers/test_calendly_token_fetch.py', 'w') as f:
        f.write(content)
    
    print("Fixed Calendly tests!")

if __name__ == "__main__":
    fix_google_tests()
    fix_calendly_tests()
