"""
Shared HTTP client utilities.

This module provides reusable async HTTP client functionality with
timeout and retry logic for making requests to external services.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Union
import httpx
from httpx import AsyncClient, Response, HTTPStatusError, RequestError

# Configure logging
logger = logging.getLogger(__name__)

class HTTPClientError(Exception):
    """Base exception for HTTP client errors."""
    pass

class HTTPRetryableError(HTTPClientError):
    """Exception for retryable HTTP errors (5xx status codes)."""
    pass

class HTTPNonRetryableError(HTTPClientError):
    """Exception for non-retryable HTTP errors (4xx status codes)."""
    pass

class SharedHTTPClient:
    """
    Shared async HTTP client with timeout and retry logic.
    
    This client provides:
    - 5 second timeout
    - 2 retries on 5xx errors
    - Proper error handling and logging
    """
    
    def __init__(self, timeout: float = 5.0, max_retries: int = 2):
        """
        Initialize the HTTP client.
        
        Args:
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for 5xx errors
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self._client: Optional[AsyncClient] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._client = AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            http2=True
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        json: Optional[Dict[str, Any]] = None,
        data: Optional[Union[str, bytes]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Make an HTTP request with retry logic.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            headers: Request headers
            params: Query parameters
            json: JSON request body
            data: Raw request body
            **kwargs: Additional httpx request arguments
            
        Returns:
            Dict[str, Any]: JSON response data
            
        Raises:
            HTTPNonRetryableError: For 4xx errors (non-retryable)
            HTTPRetryableError: For 5xx errors after all retries exhausted
            HTTPClientError: For other HTTP errors
        """
        if not self._client:
            raise HTTPClientError("HTTP client not initialized. Use as async context manager.")
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = await self._client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    json=json,
                    data=data,
                    **kwargs
                )
                
                # Check for HTTP errors
                response.raise_for_status()
                
                # Parse JSON response
                try:
                    return response.json()
                except Exception as e:
                    logger.error(f"Failed to parse JSON response from {url}: {e}")
                    raise HTTPClientError(f"Invalid JSON response: {e}")
                
            except HTTPStatusError as e:
                status_code = e.response.status_code
                
                if 400 <= status_code < 500:
                    # 4xx errors are not retryable
                    logger.error(f"Non-retryable error {status_code} for {method} {url}")
                    raise HTTPNonRetryableError(f"HTTP {status_code}: {e.response.text}")
                
                elif 500 <= status_code < 600:
                    # 5xx errors are retryable
                    last_exception = HTTPRetryableError(f"HTTP {status_code}: {e.response.text}")
                    if attempt < self.max_retries:
                        wait_time = 2 ** attempt  # Exponential backoff
                        logger.warning(f"Retryable error {status_code} for {method} {url}, retrying in {wait_time}s")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"Max retries exhausted for {method} {url}")
                        raise last_exception
                
                else:
                    # Other status codes
                    logger.error(f"Unexpected status code {status_code} for {method} {url}")
                    raise HTTPClientError(f"HTTP {status_code}: {e.response.text}")
            
            except RequestError as e:
                # Network errors, timeouts, etc.
                last_exception = HTTPClientError(f"Request error: {e}")
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Request error for {method} {url}, retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Max retries exhausted for {method} {url}: {e}")
                    raise last_exception
        
        # This should never be reached, but just in case
        if last_exception:
            raise last_exception
        raise HTTPClientError("Unexpected error in request retry loop")
    
    async def get(self, url: str, **kwargs) -> Dict[str, Any]:
        """Make a GET request."""
        return await self.request("GET", url, **kwargs)
    
    async def post(self, url: str, **kwargs) -> Dict[str, Any]:
        """Make a POST request."""
        return await self.request("POST", url, **kwargs)
    
    async def put(self, url: str, **kwargs) -> Dict[str, Any]:
        """Make a PUT request."""
        return await self.request("PUT", url, **kwargs)
    
    async def patch(self, url: str, **kwargs) -> Dict[str, Any]:
        """Make a PATCH request."""
        return await self.request("PATCH", url, **kwargs)
    
    async def delete(self, url: str, **kwargs) -> Dict[str, Any]:
        """Make a DELETE request."""
        return await self.request("DELETE", url, **kwargs)


# Convenience function for one-off requests
async def make_request(
    method: str,
    url: str,
    timeout: float = 5.0,
    max_retries: int = 2,
    **kwargs
) -> Dict[str, Any]:
    """
    Make a one-off HTTP request with retry logic.
    
    Args:
        method: HTTP method
        url: Request URL
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries for 5xx errors
        **kwargs: Additional request arguments
        
    Returns:
        Dict[str, Any]: JSON response data
    """
    async with SharedHTTPClient(timeout=timeout, max_retries=max_retries) as client:
        return await client.request(method, url, **kwargs)
