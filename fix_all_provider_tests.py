#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix all provider tests to use unittest.mock instead of respx.
"""

import re

def fix_google_tests():
    """Fix remaining Google tests."""
    with open('tests/providers/test_google_token_fetch.py', 'r') as f:
        content = f.read()
    
    # Remove any remaining @respx.mock decorators
    content = re.sub(r'    @respx\.mock\n', '', content)
    
    # Fix tests that still use respx patterns
    test_patterns = [
        # Pattern for 404/401 error tests
        (r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\((40[14]), json=\{[^}]+\}\)\s*\)\s*\n\s*# Should raise ProviderAuthError\s*\n\s*with pytest\.raises\(ProviderAuthError\)',
         lambda m: f'''        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPNonRetryableError
            mock_get.side_effect = HTTPNonRetryableError("HTTP {m.group(2)}: Error")
            
            # Should raise ProviderAuthError
            with pytest.raises(ProviderAuthError)'''),
        
        # Pattern for 500 error tests
        (r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(500, json=\{[^}]+\}\)\s*\)\s*\n\s*# Should raise AuthenticationError \(after retries\)\s*\n\s*with pytest\.raises\(AuthenticationError\)',
         lambda m: f'''        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPRetryableError
            mock_get.side_effect = HTTPRetryableError("HTTP 500: Internal server error")
            
            # Should raise AuthenticationError (after retries)
            with pytest.raises(AuthenticationError)'''),
        
        # Pattern for missing access_token test
        (r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(200, json=\{\s*"expires_at": [^}]+\s*# Missing access_token\s*\}\)\s*\)\s*\n\s*# Should raise AuthenticationError\s*\n\s*with pytest\.raises\(AuthenticationError\)',
         lambda m: f'''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {{
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
                # Missing access_token
            }}
            
            # Should raise AuthenticationError
            with pytest.raises(AuthenticationError)'''),
        
        # Pattern for default TTL test
        (r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(200, json=\{\s*"access_token": "([^"]+)"\s*# Missing expires_at\s*\}\)\s*\)',
         lambda m: f'''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {{
                "access_token": "{m.group(2)}"
                # Missing expires_at
            }}'''),
    ]
    
    for pattern, replacement in test_patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Fix calendar list test
    calendar_pattern = r'        # Mock auth-service\s*\n\s*respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(200, json=\{\s*"access_token": "([^"]+)",\s*"expires_at": [^}]+\}\)\s*\)\s*\n\s*# Mock Google Calendar API\s*\n\s*calendar_mock = respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(200, json=\{[^}]+\}\)\s*\)'
    
    def replace_calendar_test(match):
        auth_url = match.group(1)
        token = match.group(2)
        calendar_url = match.group(3)
        
        return f'''        # Mock the SharedHTTPClient methods
        with patch('shared.http.SharedHTTPClient.get') as mock_get, \\
             patch('shared.http.SharedHTTPClient.request') as mock_request:
            
            # Mock auth-service response
            mock_get.return_value = {{
                "access_token": "{token}",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }}
            
            # Mock Google Calendar API response
            mock_request.return_value = {{
                "items": [
                    {{
                        "id": "primary",
                        "summary": "Test Calendar",
                        "primary": True
                    }}
                ]
            }}'''
    
    content = re.sub(calendar_pattern, replace_calendar_test, content, flags=re.MULTILINE | re.DOTALL)
    
    with open('tests/providers/test_google_token_fetch.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed Google tests!")

def fix_calendly_tests():
    """Fix remaining Calendly tests."""
    with open('tests/providers/test_calendly_token_fetch.py', 'r') as f:
        content = f.read()
    
    # Remove any remaining @respx.mock decorators
    content = re.sub(r'    @respx\.mock\n', '', content)
    
    # Remove any remaining respx imports
    content = re.sub(r'import respx\n', '', content)
    
    # Fix malformed test methods - remove extra indentation and respx calls
    content = re.sub(r'        respx\.get\([^)]+\)\.mock\([^)]+\)\s*\n\s*# Should raise', '        # Mock the SharedHTTPClient.get method\n        with patch(\'shared.http.SharedHTTPClient.get\') as mock_get:\n            from shared.http import HTTPNonRetryableError\n            mock_get.side_effect = HTTPNonRetryableError("HTTP 404: Error")\n            \n            # Should raise', content)
    
    # Fix remaining respx patterns
    content = re.sub(r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\(200, json=\{[^}]+\}\)\s*\)', 
                     '''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "test-token",
                "expires_at": int((datetime.now() + timedelta(seconds=3600)).timestamp())
            }''', content)
    
    # Fix indentation issues
    content = re.sub(r'\n            # Should raise', '\n        # Should raise', content)
    content = re.sub(r'\n            with pytest\.raises', '\n        with pytest.raises', content)
    content = re.sub(r'\n            assert', '\n        assert', content)
    
    with open('tests/providers/test_calendly_token_fetch.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed Calendly tests!")

if __name__ == "__main__":
    print("🔧 Fixing all provider tests...")
    fix_google_tests()
    fix_calendly_tests()
    print("✅ All tests fixed!")
