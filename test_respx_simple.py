#!/usr/bin/env python3
"""
Simple test to verify respx works with httpx.
"""

import asyncio
import httpx
import respx
import pytest

async def test_simple_respx():
    """Test that respx works with httpx."""
    async with respx.mock:
        # Mock a simple request
        respx.get("http://example.com/test").mock(
            return_value=httpx.Response(200, json={"message": "success"})
        )

        # Make the request
        async with httpx.AsyncClient() as client:
            response = await client.get("http://example.com/test")
            data = response.json()

        assert data["message"] == "success"
        print("✅ Simple respx test passed!")

async def test_shared_http_client():
    """Test our SharedHTTPClient with respx."""
    import sys
    sys.path.insert(0, '.')

    from shared.http import SharedHTTPClient

    with respx.mock:
        # Mock the request
        mock_route = respx.get("http://example.com/test").mock(
            return_value=httpx.Response(200, json={"message": "success"})
        )

        # Make the request using our client
        async with SharedHTTPClient() as client:
            response = await client.get("http://example.com/test")

        assert response["message"] == "success"
        assert mock_route.called
        print("✅ SharedHTTPClient with respx test passed!")

async def main():
    """Run all tests."""
    print("🧪 Testing respx compatibility...\n")

    try:
        await test_simple_respx()
        await test_shared_http_client()
        print("\n✅ All respx tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
