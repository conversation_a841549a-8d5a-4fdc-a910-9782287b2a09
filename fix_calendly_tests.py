#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the remaining Calendly tests to use @respx.mock decorator.
"""

import re

def fix_calendly_tests():
    """Fix the Calendly test file."""
    with open('tests/providers/test_calendly_token_fetch.py', 'r') as f:
        content = f.read()
    
    # Pattern to match test methods with respx.mock context manager
    pattern = r'(    async def test_[^:]+:\n        """[^"]*"""\n)        with respx\.mock:\n((?:            [^\n]*\n)*)'
    
    def replace_func(match):
        method_def = match.group(1)
        indented_content = match.group(2)
        
        # Remove one level of indentation from the content
        unindented_content = re.sub(r'^            ', '        ', indented_content, flags=re.MULTILINE)
        
        # Add the decorator
        return f'    @respx.mock\n{method_def}{unindented_content}'
    
    # Apply the replacement
    fixed_content = re.sub(pattern, replace_func, content, flags=re.MULTILINE)
    
    with open('tests/providers/test_calendly_token_fetch.py', 'w') as f:
        f.write(fixed_content)
    
    print("Fixed Calendly tests!")

if __name__ == "__main__":
    fix_calendly_tests()
